# Zsh 自动安装配置脚本

一个功能强大的跨平台 Zsh 安装和配置脚本，支持多种 Linux 发行版和 macOS，一键完成 Zsh + Oh My Zsh + 常用插件的安装配置。

## 🚀 特性

### 📋 支持的操作系统
- **Debian/Ubuntu** - 使用 `apt` 包管理器
- **RedHat/CentOS** - 使用 `yum` 包管理器  
- **Fedora** - 使用 `dnf` 包管理器
- **Arch Linux** - 使用 `pacman` 包管理器
- **Alpine Linux** - 使用 `apk` 包管理器
- **openSUSE** - 使用 `zypper` 包管理器
- **macOS** - 使用 `brew` 包管理器

### 🔧 自动安装组件
- **Zsh Shell** - 现代化的命令行 Shell
- **Oh My Zsh** - 强大的 Zsh 框架
- **zsh-autosuggestions** - 基于历史的命令自动建议
- **zsh-syntax-highlighting** - 实时语法高亮
- **zsh-completions** - 增强的命令补全功能

### ⚡ 智能特性
- **自动系统检测** - 智能识别操作系统并选择合适的包管理器
- **多源安装** - Oh My Zsh 和插件都配置了多个备用源，提高安装成功率
- **配置备份** - 自动备份现有的 `.zshrc` 配置文件
- **错误恢复** - 智能错误处理，单个组件失败不影响整体安装
- **彩色日志** - 清晰的彩色日志输出，便于跟踪安装进度

### 🛡️ 安全保障
- **权限检查** - 禁止 root 用户直接运行，确保安全性
- **智能 sudo** - 仅在需要时请求 sudo 权限
- **配置保护** - 自动备份现有配置，防止数据丢失
- **组件检测** - 智能跳过已安装的组件，避免重复安装

## 📦 安装内容

### 基础工具
- `curl` - 用于下载安装脚本
- `git` - 用于克隆插件仓库
- `zsh` - 目标 Shell

### Zsh 配置优化
- **历史记录优化** - 10万条历史记录，去重和共享
- **自动补全增强** - 菜单选择和大小写不敏感匹配
- **便捷别名** - 常用命令的简化别名
- **现代工具集成** - 自动检测并使用 `exa`、`bat` 等现代化工具
- **跨平台兼容** - 针对不同系统优化的别名设置

## 🎯 使用方法

### 快速安装
```bash
# 下载脚本
curl -fsSL https://your-repo/install_zsh.sh -o install_zsh.sh

# 添加执行权限
chmod +x install_zsh.sh

# 运行安装（使用普通用户，不要用 sudo）
./install_zsh.sh
```

### 启用 Zsh
安装完成后，选择以下方式之一启用 Zsh：

```bash
# 方式1: 立即切换到 zsh（推荐）
exec zsh

# 方式2: 重新加载配置
source ~/.zshrc

# 方式3: 重新登录或重启终端
```

### 验证安装
```bash
# 查看当前 shell
echo $SHELL

# 查看 zsh 版本
zsh --version

# 测试插件功能
# 输入命令时应该看到语法高亮和自动建议
```

## 🔍 安装过程

脚本会按以下顺序执行：

1. **系统检测** - 自动识别操作系统类型
2. **权限验证** - 确保以普通用户身份运行
3. **依赖安装** - 安装 curl、git、zsh 等基础工具
4. **Oh My Zsh 安装** - 从多个源尝试安装 Oh My Zsh 框架
5. **插件安装** - 安装三个核心插件
6. **配置生成** - 生成优化的 `.zshrc` 配置文件
7. **默认 Shell 设置** - 将 zsh 设置为默认 Shell

## 🛠️ 推荐的额外工具

安装完成后，建议安装以下现代化命令行工具：

### Debian/Ubuntu
```bash
sudo apt install exa bat fd-find fzf
```

### Fedora
```bash
sudo dnf install exa bat fd-find fzf
```

### Arch Linux
```bash
sudo pacman -S exa bat fd fzf
```

### macOS
```bash
brew install exa bat fd fzf
```

## 📝 配置说明

### 插件功能
- **zsh-autosuggestions**: 根据历史命令提供灰色的自动建议
- **zsh-syntax-highlighting**: 实时高亮命令语法，错误命令显示红色
- **zsh-completions**: 提供更多命令的补全支持

### 优化设置
- **autocd**: 输入目录名直接切换目录
- **correct_all**: 自动纠正拼写错误
- **hist_ignore_all_dups**: 历史记录去重
- **share_history**: 多个终端共享历史记录

## 🔧 故障排除

### 常见问题

**Q: 提示权限错误**
```bash
# 错误用法
sudo ./install_zsh.sh

# 正确用法  
./install_zsh.sh
```

**Q: 插件不工作**
```bash
# 重新加载配置
source ~/.zshrc

# 或重启终端
exec zsh
```

**Q: 恢复原配置**
```bash
# 查找备份文件
ls ~/.zshrc.bak-*

# 恢复备份
cp ~/.zshrc.bak-YYYYMMDD-HHMMSS ~/.zshrc
```

## 🎨 效果预览

安装完成后，你的终端将拥有：
- 🎯 **智能提示符** - 显示当前目录和 Git 状态
- 🌈 **语法高亮** - 命令、参数、路径等不同颜色显示
- 💡 **自动建议** - 基于历史的命令建议（灰色文字）
- ⚡ **快速补全** - Tab 键触发的智能补全菜单
- 📁 **便捷导航** - 直接输入目录名即可切换

## 🔄 更新和维护

### 更新 Oh My Zsh
```bash
omz update
```

### 更新插件
```bash
cd ~/.oh-my-zsh/custom/plugins/zsh-autosuggestions && git pull
cd ~/.oh-my-zsh/custom/plugins/zsh-syntax-highlighting && git pull
cd ~/.oh-my-zsh/custom/plugins/zsh-completions && git pull
```

### 添加更多插件
```bash
# 克隆新插件到 custom/plugins 目录
git clone https://github.com/plugin-name ~/.oh-my-zsh/custom/plugins/plugin-name

# 编辑 ~/.zshrc 添加插件名到 plugins 数组
plugins=(git zsh-autosuggestions zsh-syntax-highlighting zsh-completions plugin-name)

# 重新加载配置
source ~/.zshrc
```

## 📊 性能优化

脚本已包含以下性能优化：
- **浅克隆** - 使用 `--depth=1` 减少下载时间
- **静默安装** - 减少不必要的输出
- **并行处理** - 多个插件可以并行安装
- **缓存优化** - 智能跳过已安装组件

## 🌟 高级配置

### 自定义主题
```bash
# 编辑 ~/.zshrc 修改主题
ZSH_THEME="agnoster"  # 或其他喜欢的主题

# 重新加载
source ~/.zshrc
```

### 自定义别名
```bash
# 在 ~/.zshrc 末尾添加个人别名
alias ll="ls -la"
alias la="ls -A"
alias ..="cd .."
alias ...="cd ../.."
```

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

### 贡献指南
1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📞 支持

如果遇到问题，请：
1. 查看 [故障排除](#-故障排除) 部分
2. 搜索现有的 [Issues](https://github.com/your-repo/issues)
3. 创建新的 Issue 并提供详细信息

---

**享受你的新 Zsh 环境！** 🎉

> 💡 **提示**: 首次启动 zsh 可能需要几秒钟来初始化插件，这是正常现象。
