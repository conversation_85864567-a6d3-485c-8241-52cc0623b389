#!/bin/bash
# Debian 一键安装并配置 Git 的脚本

# 1. 更新系统包列表
echo "=== 正在更新系统包列表 ==="
sudo apt update -qq

# 2. 安装 Git（通过 Debian 官方仓库，稳定可靠）
echo -e "\n=== 正在安装 Git ==="
sudo apt install -y -qq git

# 3. 验证安装结果
echo -e "\n=== 验证 Git 安装 ==="
git --version
if [ $? -eq 0 ]; then
    echo "Git 安装成功！"
else
    echo "Git 安装失败，请检查网络或权限后重试"
    exit 1
fi

# 4. （可选）配置 Git 基础信息（用户名和邮箱，提交代码时必需）
read -p "是否需要配置 Git 用户名和邮箱？(y/n): " config_choice
if [ "$config_choice" = "y" ] || [ "$config_choice" = "Y" ]; then
    read -p "请输入你的 Git 用户名（如：张三）: " git_name
    read -p "请输入你的 Git 邮箱（如：<EMAIL>）: " git_email
    
    # 配置全局 Git 信息
    git config --global user.name "$git_name"
    git config --global user.email "$git_email"
    
    # 配置默认文本编辑器（可选，这里用 nano，适合新手）
    git config --global core.editor "nano"
    
    echo -e "\nGit 基础信息配置完成，可通过 'git config --list' 查看"
fi

echo -e "\n=== 操作结束 ==="
echo "常用 Git 命令参考："
echo "  git init       # 在当前目录初始化 Git 仓库"
echo "  git clone <链接> # 克隆远程仓库（如 GitHub 项目）"
echo "  git add .      # 将当前目录所有修改加入暂存区"
echo "  git commit -m '备注' # 提交暂存区的修改"