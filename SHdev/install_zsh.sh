#!/bin/bash

# 检查是否为普通用户（非root）
if [ "$EUID" -eq 0 ]; then
    echo "错误：请使用普通用户运行，需有sudo权限"
    exit 1
fi

# 检查sudo权限
if ! sudo -n true 2>/dev/null; then
    echo "错误：当前用户没有sudo权限，请先配置"
    exit 1
fi

# 安装基础依赖
echo "=== 安装基础工具 ==="
sudo apt update -qq
sudo apt install -y -qq curl git zsh

# 安装Oh My Zsh
echo -e "\n=== 安装Oh My Zsh ==="
sh -c "$(curl -fsSL https://gitee.com/allenjia09/ohmyzsh/master/tools/install.sh)" "" --unattended

# 定义插件目录
ZSH_CUSTOM=${ZSH_CUSTOM:-$HOME/.oh-my-zsh/custom}

# 安装插件
echo -e "\n=== 安装必备插件 ==="
[ ! -d "$ZSH_CUSTOM/plugins/zsh-autosuggestions" ] && git clone -q https://gitee.com/allenjia09/zsh-autosuggestions.git "$ZSH_CUSTOM/plugins/zsh-autosuggestions"
[ ! -d "$ZSH_CUSTOM/plugins/zsh-syntax-highlighting" ] && git clone -q https://gitee.com/allenjia09/zsh-syntax-highlighting.git "$ZSH_CUSTOM/plugins/zsh-syntax-highlighting"
[ ! -d "$ZSH_CUSTOM/plugins/zsh-completions" ] && git clone -q https://gitee.com/allenjia09/zsh-completions.git "$ZSH_CUSTOM/plugins/zsh-completions"

# 配置.zshrc
echo -e "\n=== 配置Zsh ==="
[ -f "$HOME/.zshrc" ] && cp "$HOME/.zshrc" "$HOME/.zshrc.bak-$(date +%Y%m%d)"
sed -i 's/^plugins=(git)/plugins=(git zsh-autosuggestions zsh-syntax-highlighting zsh-completions)/' "$HOME/.zshrc"

# 添加优化配置
cat << 'EOF' >> "$HOME/.zshrc"

# 额外优化
setopt autocd
setopt correct_all
HISTSIZE=100000
SAVEHIST=100000
HISTFILE=~/.zsh_history
setopt hist_ignore_all_dups

# 补全设置
autoload -U compinit && compinit
zstyle ':completion:*' menu select
EOF

# 设置默认Shell
echo -e "\n=== 设置默认Shell ==="
sudo sh -c "echo $(which zsh) >> /etc/shells" 2>/dev/null
chsh -s $(which zsh)

echo -e "\n=== 安装完成 ==="
echo "执行 'exec zsh' 立即生效，或重启终端"
