#!/bin/bash

# 检查是否为普通用户（非root）
if [ "$EUID" -eq 0 ]; then
    echo "错误：请使用普通用户运行，需有sudo权限"
    exit 1
fi

# 检查sudo权限
if ! sudo -n true 2>/dev/null; then
    echo "错误：当前用户没有sudo权限，请先配置"
    exit 1
fi

# 安装基础依赖
echo "=== 安装基础工具 ==="
sudo apt update -qq
sudo apt install -y -qq curl git zsh

# 安装Oh My Zsh
echo -e "\n=== 安装Oh My Zsh ==="
if [ ! -d "$HOME/.oh-my-zsh" ]; then
    sh -c "$(curl -fsSL https://gitee.com/allenjia09/ohmyzsh/raw/master/tools/install.sh)" "" --unattended
else
    echo "Oh My Zsh已存在，跳过安装"
fi

# 定义插件目录
ZSH_CUSTOM=${ZSH_CUSTOM:-$HOME/.oh-my-zsh/custom}

# 安装插件
echo -e "\n=== 安装必备插件 ==="
install_plugin() {
    local plugin_name="$1"
    local plugin_url="$2"
    local plugin_dir="$ZSH_CUSTOM/plugins/$plugin_name"

    if [ ! -d "$plugin_dir" ]; then
        echo "安装插件: $plugin_name"
        if git clone -q "$plugin_url" "$plugin_dir"; then
            echo "✓ $plugin_name 安装成功"
        else
            echo "✗ $plugin_name 安装失败"
        fi
    else
        echo "插件 $plugin_name 已存在，跳过"
    fi
}

install_plugin "zsh-autosuggestions" "https://gitee.com/allenjia09/zsh-autosuggestions.git"
install_plugin "zsh-syntax-highlighting" "https://gitee.com/allenjia09/zsh-syntax-highlighting.git"
install_plugin "zsh-completions" "https://gitee.com/allenjia09/zsh-completions.git"

# 配置.zshrc
echo -e "\n=== 配置Zsh ==="
if [ -f "$HOME/.zshrc" ]; then
    cp "$HOME/.zshrc" "$HOME/.zshrc.bak-$(date +%Y%m%d-%H%M%S)"
    echo "已备份现有.zshrc配置"
fi

# 更新插件配置，使用更兼容的方式
if [ -f "$HOME/.zshrc" ]; then
    if grep -q "^plugins=" "$HOME/.zshrc"; then
        # 使用临时文件避免sed的兼容性问题
        sed 's/^plugins=.*/plugins=(git zsh-autosuggestions zsh-syntax-highlighting zsh-completions)/' "$HOME/.zshrc" > "$HOME/.zshrc.tmp"
        mv "$HOME/.zshrc.tmp" "$HOME/.zshrc"
    else
        echo 'plugins=(git zsh-autosuggestions zsh-syntax-highlighting zsh-completions)' >> "$HOME/.zshrc"
    fi
fi

# 添加优化配置
cat << 'EOF' >> "$HOME/.zshrc"

# === Zsh优化配置 ===
# 自动切换目录
setopt autocd
# 自动纠错
setopt correct_all
# 历史记录设置
HISTSIZE=100000
SAVEHIST=100000
HISTFILE=~/.zsh_history
setopt hist_ignore_all_dups
setopt hist_ignore_space
setopt share_history

# 补全设置
autoload -U compinit && compinit
zstyle ':completion:*' menu select
zstyle ':completion:*' matcher-list 'm:{a-zA-Z}={A-Za-z}'

# 常用别名
alias ll='ls -alF'
alias la='ls -A'
alias l='ls -CF'
alias grep='grep --color=auto'
EOF

# 设置默认Shell
echo -e "\n=== 设置默认Shell ==="
ZSH_PATH=$(which zsh)
if [ -n "$ZSH_PATH" ]; then
    # 检查zsh是否已在/etc/shells中
    if ! grep -q "$ZSH_PATH" /etc/shells 2>/dev/null; then
        echo "将zsh添加到/etc/shells..."
        echo "$ZSH_PATH" | sudo tee -a /etc/shells > /dev/null
    fi

    # 更改默认shell
    if [ "$SHELL" != "$ZSH_PATH" ]; then
        echo "设置zsh为默认shell..."
        chsh -s "$ZSH_PATH"
    else
        echo "zsh已经是默认shell"
    fi
else
    echo "错误：找不到zsh可执行文件"
    exit 1
fi

echo -e "\n=== 安装完成 ==="
echo "请执行以下命令之一来启用新配置:"
echo "  1. exec zsh          # 立即切换到zsh"
echo "  2. source ~/.zshrc   # 重新加载配置"
echo "  3. 重启终端"
echo ""
echo "建议安装的额外工具:"
echo "  - exa: sudo apt install exa"
echo "  - bat: sudo apt install bat"
echo "  - fd-find: sudo apt install fd-find"
