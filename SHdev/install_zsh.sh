#!/bin/bash

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检测操作系统
detect_os() {
    if [[ "$OSTYPE" == "darwin"* ]]; then
        echo "macos"
    elif [[ -f /etc/debian_version ]]; then
        echo "debian"
    elif [[ -f /etc/redhat-release ]]; then
        echo "redhat"
    elif [[ -f /etc/arch-release ]]; then
        echo "arch"
    else
        echo "unknown"
    fi
}

# 检查是否为普通用户（非root）
check_user() {
    if [ "$EUID" -eq 0 ]; then
        log_error "请使用普通用户运行此脚本"
        exit 1
    fi
}

# 检查必要的命令
check_command() {
    if ! command -v "$1" &> /dev/null; then
        log_error "命令 '$1' 未找到，请先安装"
        return 1
    fi
    return 0
}

# 安装基础依赖
install_dependencies() {
    local os_type=$(detect_os)
    log_info "检测到操作系统: $os_type"

    case $os_type in
        "macos")
            log_info "在macOS上安装依赖..."
            if ! check_command "brew"; then
                log_info "安装Homebrew..."
                /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
            fi
            brew update
            brew install curl git zsh
            ;;
        "debian")
            log_info "在Debian/Ubuntu上安装依赖..."
            if ! sudo -n true 2>/dev/null; then
                log_error "当前用户没有sudo权限，请先配置"
                exit 1
            fi
            sudo apt update -qq
            sudo apt install -y -qq curl git zsh
            ;;
        "redhat")
            log_info "在RedHat/CentOS上安装依赖..."
            if ! sudo -n true 2>/dev/null; then
                log_error "当前用户没有sudo权限，请先配置"
                exit 1
            fi
            sudo yum install -y curl git zsh || sudo dnf install -y curl git zsh
            ;;
        "arch")
            log_info "在Arch Linux上安装依赖..."
            if ! sudo -n true 2>/dev/null; then
                log_error "当前用户没有sudo权限，请先配置"
                exit 1
            fi
            sudo pacman -S --noconfirm curl git zsh
            ;;
        *)
            log_error "不支持的操作系统，请手动安装 curl, git, zsh"
            exit 1
            ;;
    esac

    log_success "基础工具安装完成"
}

# 安装Oh My Zsh
install_oh_my_zsh() {
    log_info "安装Oh My Zsh..."

    if [ -d "$HOME/.oh-my-zsh" ]; then
        log_warning "Oh My Zsh已存在，跳过安装"
        return 0
    fi

    # 尝试多个源，提高成功率
    local install_urls=(
        "https://raw.githubusercontent.com/ohmyzsh/ohmyzsh/master/tools/install.sh"
        "https://gitee.com/mirrors/oh-my-zsh/raw/master/tools/install.sh"
        "https://gitee.com/allenjia09/ohmyzsh/raw/master/tools/install.sh"
    )

    for url in "${install_urls[@]}"; do
        log_info "尝试从 $url 安装..."
        if sh -c "$(curl -fsSL $url)" "" --unattended 2>/dev/null; then
            log_success "Oh My Zsh安装成功"
            return 0
        else
            log_warning "从 $url 安装失败，尝试下一个源..."
        fi
    done

    log_error "所有Oh My Zsh安装源都失败了"
    exit 1
}

# 安装插件
install_plugins() {
    log_info "安装Zsh插件..."

    # 定义插件目录
    local ZSH_CUSTOM=${ZSH_CUSTOM:-$HOME/.oh-my-zsh/custom}

    if [ ! -d "$ZSH_CUSTOM" ]; then
        log_error "Oh My Zsh custom目录不存在: $ZSH_CUSTOM"
        exit 1
    fi

    # 插件列表：名称|GitHub仓库|Gitee镜像
    local plugins=(
        "zsh-autosuggestions|https://github.com/zsh-users/zsh-autosuggestions.git|https://gitee.com/allenjia09/zsh-autosuggestions.git"
        "zsh-syntax-highlighting|https://github.com/zsh-users/zsh-syntax-highlighting.git|https://gitee.com/allenjia09/zsh-syntax-highlighting.git"
        "zsh-completions|https://github.com/zsh-users/zsh-completions.git|https://gitee.com/allenjia09/zsh-completions.git"
    )

    for plugin_info in "${plugins[@]}"; do
        IFS='|' read -r plugin_name github_url gitee_url <<< "$plugin_info"
        local plugin_dir="$ZSH_CUSTOM/plugins/$plugin_name"

        if [ -d "$plugin_dir" ]; then
            log_warning "插件 $plugin_name 已存在，跳过安装"
            continue
        fi

        log_info "安装插件: $plugin_name"

        # 先尝试GitHub，再尝试Gitee
        if git clone --depth=1 -q "$github_url" "$plugin_dir" 2>/dev/null; then
            log_success "插件 $plugin_name 安装成功 (GitHub)"
        elif git clone --depth=1 -q "$gitee_url" "$plugin_dir" 2>/dev/null; then
            log_success "插件 $plugin_name 安装成功 (Gitee)"
        else
            log_error "插件 $plugin_name 安装失败"
            # 不退出，继续安装其他插件
        fi
    done
}

# 配置.zshrc
echo -e "\n=== 配置Zsh ==="
[ -f "$HOME/.zshrc" ] && cp "$HOME/.zshrc" "$HOME/.zshrc.bak-$(date +%Y%m%d)"
sed -i 's/^plugins=(git)/plugins=(git zsh-autosuggestions zsh-syntax-highlighting zsh-completions)/' "$HOME/.zshrc"

# 添加优化配置
cat << 'EOF' >> "$HOME/.zshrc"

# 额外优化
setopt autocd
setopt correct_all
HISTSIZE=100000
SAVEHIST=100000
HISTFILE=~/.zsh_history
setopt hist_ignore_all_dups

# 补全设置
autoload -U compinit && compinit
zstyle ':completion:*' menu select
EOF

# 设置默认Shell
echo -e "\n=== 设置默认Shell ==="
sudo sh -c "echo $(which zsh) >> /etc/shells" 2>/dev/null
chsh -s $(which zsh)

echo -e "\n=== 安装完成 ==="
echo "执行 'exec zsh' 立即生效，或重启终端"
